"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(home)/page",{

/***/ "(app-pages-browser)/./src/app/components/SimpleHome.js":
/*!******************************************!*\
  !*** ./src/app/components/SimpleHome.js ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleHome)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AnimatedHeading */ \"(app-pages-browser)/./src/app/components/AnimatedHeading.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst sections = [\n    {\n        id: \"hero\",\n        title: \"Exquisite Lab Grown Diamonds\",\n        subtitle: \"Every Diamond Tells a Story\",\n        description: \"Ethically sourced, certified diamonds with unparalleled quality and brilliance.\",\n        image: \"/image/tache_diamond_rough_cut.png\",\n        cta: \"Discover Our Collection\"\n    },\n    {\n        id: \"shapes\",\n        title: \"Diamond Shapes\",\n        subtitle: \"Classic & Contemporary\",\n        description: \"Round, princess, emerald, oval and more. Find your perfect cut.\",\n        image: \"/image/tache_lesotho_icon-768x752.jpg\",\n        cta: \"View Shapes\"\n    },\n    {\n        id: \"craftsmanship\",\n        title: \"Master Craftsmanship\",\n        subtitle: \"Precision in Every Cut\",\n        description: \"Our skilled artisans combine traditional techniques with modern technology to create diamonds of exceptional beauty and brilliance.\",\n        image: \"/image/shaping_phase_tache-768x768.png\",\n        cta: \"View Process\"\n    },\n    {\n        id: \"colors\",\n        title: \"30+ Diamond Colors\",\n        subtitle: \"Every Shade of Brilliance\",\n        description: \"From classic white to rare fancy colors. Discover diamonds in over 30 stunning colors.\",\n        image: \"/images/Colors/natural-color-dmds-color-diamonds-rough-diamonds-removebg-preview.png\",\n        cta: \"View Colors\"\n    }\n];\nfunction SimpleHome() {\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHome.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"SimpleHome.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"full-section relative w-full h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/image/Homepage_Banner_2025_Desktop_c0ddfa8194.png\",\n                        alt: \"Diamond Atelier Homepage Banner\",\n                        fill: true,\n                        className: \"absolute inset-0 w-full h-full object-cover\",\n                        priority: true,\n                        quality: 90\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/60 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                        children: [\n                            ...Array(12)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white rounded-full opacity-60\",\n                                initial: {\n                                    x: Math.random() * window.innerWidth,\n                                    y: Math.random() * window.innerHeight,\n                                    scale: 0\n                                },\n                                animate: {\n                                    y: [\n                                        null,\n                                        -100\n                                    ],\n                                    opacity: [\n                                        0.6,\n                                        0\n                                    ],\n                                    scale: [\n                                        0,\n                                        1,\n                                        0\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3 + Math.random() * 2,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 3,\n                                    ease: \"easeOut\"\n                                }\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex flex-col items-center justify-center z-20 px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"text-center mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-xl font-light leading-tight tracking-[0.2em] text-black/95 transition-all duration-700 font-montserrat\",\n                                            style: {\n                                                // textShadow: '0 0 40px rgba(255,255,255,0.6), 0 0 80px rgba(255,255,255,0.3)',\n                                                letterSpacing: \"0.2em\"\n                                            },\n                                            children: \"DIAMONDS THAT DESERVE YOU\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    delay: 0.5,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"text-center max-w-4xl mx-auto mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg sm:text-xl md:text-2xl lg:text-2xl xl:text-3xl font-light text-black/90 tracking-[0.3em] leading-relaxed font-montserrat\",\n                                        children: [\n                                            \" \",\n                                            \"10,000+ Certified Stones\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 h-px bg-gradient-to-r from-transparent via-black/60 to-transparent mx-auto mt-6 mb-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base md:text-lg lg:text-xl text-black/70 font-light tracking-wide leading-relaxed font-montserrat\",\n                                        children: \"Where precision meets perfection in every facet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    delay: 1,\n                                    ease: \"easeOut\"\n                                },\n                                className: \"flex flex-col sm:flex-row gap-4 sm:gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/shapes\",\n                                        className: \"group relative px-8 py-3 bg-black border border-white/30 text-white hover:bg-white hover:text-black transition-all duration-500 text-sm sm:text-base tracking-wider font-light overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: \"EXPLORE SHAPES\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"https://inventory.diamondatelier.in/\",\n                                        className: \"group relative px-8 py-3 bg-black backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-500 text-sm sm:text-base tracking-wider font-light\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10\",\n                                            children: \"VIEW INVENTORY\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 1.5,\n                            delay: 1.5\n                        },\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 cursor-pointer\",\n                        onClick: ()=>setCurrentIndex(1),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            animate: {\n                                y: [\n                                    0,\n                                    8,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            },\n                            className: \"flex flex-col items-center text-white/60 hover:text-white transition-colors duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs tracking-widest mb-2 font-light\",\n                                    children: \"SCROLL\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"min-h-screen flex items-center justify-center px-4 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center justify-between gap-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-black\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                                children: sections[0].subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            text: sections[0].title,\n                                            level: \"h1\",\n                                            className: \"text-5xl xl:text-6xl 2xl:text-7xl mb-8 font-light leading-tight font-montserrat\",\n                                            showUnderline: false,\n                                            animationDelay: 0.2,\n                                            triggerOnMount: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl xl:text-2xl leading-relaxed font-light text-gray-700 mb-12 max-w-2xl font-montserrat\",\n                                            children: sections[0].description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat\",\n                                            children: sections[0].cta\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-96 h-96 xl:w-[500px] xl:h-[500px] 2xl:w-[600px] 2xl:h-[600px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: sections[0].image,\n                                            alt: sections[0].title,\n                                            fill: true,\n                                            className: \"object-cover rounded-full\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden text-center text-black\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                        children: sections[0].subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnimatedHeading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    text: sections[0].title,\n                                    level: \"h1\",\n                                    className: \"text-4xl sm:text-5xl mb-8 font-light leading-tight font-montserrat\",\n                                    showUnderline: false,\n                                    animationDelay: 0.2,\n                                    triggerOnMount: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-80 h-80 sm:w-96 sm:h-96\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: sections[0].image,\n                                            alt: sections[0].title,\n                                            fill: true,\n                                            className: \"object-cover rounded-full\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg leading-relaxed font-light text-gray-700 mb-8 max-w-2xl mx-auto font-montserrat\",\n                                    children: sections[0].description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat\",\n                                    children: sections[0].cta\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            sections.slice(1).map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"min-h-screen flex items-center justify-center px-4 lg:px-8 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center justify-between gap-16 \".concat(index % 2 === 0 ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-black\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                                    children: section.subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl xl:text-5xl mb-6 font-light leading-tight font-montserrat\",\n                                                children: section.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg xl:text-xl leading-relaxed font-light text-gray-700 mb-8 max-w-2xl font-montserrat\",\n                                                children: section.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat\",\n                                                children: section.cta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-80 h-80 xl:w-96 xl:h-96\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: section.image,\n                                                alt: section.title,\n                                                fill: true,\n                                                className: \"object-cover rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden text-center text-black\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat\",\n                                            children: section.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl mb-6 font-light leading-tight font-montserrat\",\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-72 h-72 sm:w-80 sm:h-80\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: section.image,\n                                                alt: section.title,\n                                                fill: true,\n                                                className: \"object-cover rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base leading-relaxed font-light text-gray-700 mb-6 max-w-2xl mx-auto font-montserrat\",\n                                        children: section.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat\",\n                                        children: section.cta\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this)\n                }, section.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative w-full h-96 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat\",\n                        style: {\n                            backgroundImage: \"url('/image/Expert.jpg')\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/40 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-black mb-6 drop-shadow-lg\",\n                                        children: \"Expert Eyes on Every Stone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-black mb-8 leading-relaxed drop-shadow-md\",\n                                        children: \"Our in-house gemologists personally inspect and verify every lab-grown diamond. You'll receive a detailed report covering brilliance, cut, and quality — far beyond what a certificate alone can show.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n                lineNumber: 345,\n                columnNumber: 1\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\New folder\\\\diamond-atelier-website-update-master\\\\src\\\\app\\\\components\\\\SimpleHome.js\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHome, \"2oCXE2WiAW05zG4oFnHELxlAGiA=\");\n_c = SimpleHome;\nvar _c;\n$RefreshReg$(_c, \"SimpleHome\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/SimpleHome.js\n"));

/***/ })

});