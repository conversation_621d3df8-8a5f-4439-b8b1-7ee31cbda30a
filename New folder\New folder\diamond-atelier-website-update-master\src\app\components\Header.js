"use client";

import React, { useState, useEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { Menu, X, Search, User, ShoppingBag } from "lucide-react";

const Header = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const menuRef = useRef(null);
  const hamburgerRef = useRef(null);
  const pathname = usePathname();

  // Navigation routes - Luxury brand style
  const navigationRoutes = [
    { id: 1, label: "Shapes", path: "/shapes" },
    {
      id: 2,
      label: "INVENTORY",
      path: "https://inventory.diamondatelier.in/",
      external: true,
    },
    { id: 3, label: "BLOGS", path: "/blogs" },
    { id: 4, label: "EDUCATION", path: "/education" },
    { id: 5, label: "ABOUT", path: "/about" },
    { id: 6, label: "CONTACT", path: "/contact" },
  ];

  // Ultra-optimized scroll detection with RAF and throttling
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const scrollY = window.scrollY;
          setIsScrolled(scrollY > 20);
          ticking = false;
        });
        ticking = true;
      }
    };


    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        hamburgerRef.current &&
        !hamburgerRef.current.contains(event.target)
      ) {
        setMenuOpen(false);
      }
    };

    if (menuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "unset";
    };
  }, [menuOpen]);

  const toggleMenu = () => setMenuOpen(!menuOpen);
  const closeMenu = () => setMenuOpen(false);
  const isActive = (item) => pathname === item.path;

  return (
    <>
      <header
        className={`sticky top-0 w-full z-50 transition-all duration-75 ease-out ${
          isScrolled
            ? "bg-white backdrop-blur-xl shadow-2xl border-b border-gray-300"
            : "bg-white backdrop-blur-md"
        }`}
        style={{
          boxShadow: isScrolled
            ? "0 10px 40px rgba(0,0,0,0.5), inset 0 1px 0 rgba(255,255,255,0.1)"
            : "0 5px 20px rgba(0,0,0,0.2)",
          WebkitBackdropFilter: isScrolled ? "blur(20px)" : "blur(10px)",
          backdropFilter: isScrolled ? "blur(20px)" : "blur(10px)",
          willChange: "transform",
          transform: "translateZ(0)", // Force GPU acceleration
          contain: "layout style paint"
        }}
      >
        <div className="max-w-7xl mx-auto px-16 sm:px-6 lg:px-8 mobile-header-container">
          <div className={`transition-all duration-75 ${isScrolled ? 'opacity-0 h-0 overflow-hidden' : 'opacity-100'}`} style={{ willChange: 'opacity, height' }}>
            <div className="flex justify-center py-3 lg:py-4 px-4">
              <Link href="/" className="group relative block">
                <div className="relative transition-all duration-75" style={{ willChange: 'transform' }}>
                  <h1
                    className="text-black text-xl sm:text-xl md:text-2xl lg:text-3xl font-light tracking-[0.3em] uppercase transition-all duration-200 whitespace-nowrap"
                    style={{
                      fontFamily: 'Times New Roman, serif',
                      letterSpacing: '0.3em'
                    }}
                  >
                    DIAMOND ATELIER
                  </h1>

                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/8 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-xl"></div>
                </div>
              </Link>
            </div>

            <nav className="hidden lg:block pb-3">
              <div className="flex items-center justify-center space-x-8">
                {navigationRoutes.map((item) => {
                  const linkClasses = `group relative text-black hover:text-black text-sm font-medium uppercase tracking-[0.15em] transition-all duration-200 py-2 px-4 rounded-lg hover:bg-black/10 ${
                    isActive(item) ? "text-black bg-black/20 shadow-lg" : ""
                  }`;

                  const linkContent = (
                    <>
                      <span className="relative z-10">{item.label}</span>

                      <span className={`absolute bottom-0 left-1/2 w-0 h-px bg-gradient-to-r from-transparent via-black to-transparent group-hover:w-full transition-all duration-500 transform -translate-x-1/2 ${
                        isActive(item) ? 'w-full' : ''
                      }`}></span>

                      <span className="absolute inset-0 bg-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg blur-sm"></span>
                    </>
                  );

                  return item.external ? (
                    <a
                      key={item.id}
                      href={item.path}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={linkClasses}
                      style={{ fontFamily: "Times New Roman, serif" }}
                    >
                      {linkContent}
                    </a>
                  ) : (
                    <Link
                      key={item.id}
                      href={item.path}
                      className={linkClasses}
                      style={{ fontFamily: "Times New Roman, serif" }}
                    >
                      {linkContent}
                    </Link>
                  );
                })}
              </div>
            </nav>
          </div>

          <div className={`transition-all duration-75 ${isScrolled ? 'opacity-100' : 'opacity-0 h-0 overflow-hidden'}`} style={{ willChange: 'opacity, height' }}>
            <div className="flex items-center justify-center lg:justify-between py-3 lg:py-2 px-4">
              <Link href="/" className="flex-shrink-0 group">
                <div className="relative transition-all duration-75" style={{ willChange: 'transform' }}>
                  {/* Mobile: Full name, Desktop: DA */}
                  <div className="lg:hidden">
                    <h1
                      className="text-lg sm:text-xl font-light tracking-[0.25em] uppercase text-black transition-all duration-200 whitespace-nowrap"
                      style={{
                        fontFamily: 'Times New Roman, serif',
                        letterSpacing: '0.25em'
                      }}
                    >
                      DIAMOND ATELIER
                    </h1>
                  </div>

                  {/* Desktop: DA logo */}
                  <div className="hidden lg:block">
                    <div
                      className="text-2xl sm:text-3xl md:text-4xl font-light transition-all duration-200 flex items-center"
                      style={{
                        fontFamily: 'Times New Roman, serif',
                        letterSpacing: '0.05em'
                      }}
                    >
                      <span
                        className="inline-block"
                        style={{
                          color: '#000000',
                          textShadow: '0 0 10px rgba(0, 0, 0, 0.2)'
                        }}
                      >
                        D
                      </span>
                      <span
                        className="inline-block"
                        style={{
                          color: '#000000',
                          textShadow: '0 0 10px rgba(0, 0, 0, 0.2)'
                        }}
                      >
                        A
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
              <nav className="hidden lg:block flex-1 mx-8">
                <div className="flex items-center justify-center space-x-6">
                  {navigationRoutes.map((item) => {
                    const linkClasses = `group relative text-black hover:text-black text-sm font-medium uppercase tracking-[0.15em] transition-all duration-200 py-2 px-4 rounded-lg hover:bg-black/15 ${
                      isActive(item) ? "text-black bg-black/25 shadow-lg" : ""
                    }`;

                    const scrolledLinkContent = (
                      <>
                        <span className="relative z-10">{item.label}</span>
                        {/* Compact underline animation */}
                        <span className={`absolute bottom-0 left-1/2 w-0 h-px bg-gradient-to-r from-transparent via-black to-transparent group-hover:w-full transition-all duration-400 transform -translate-x-1/2 ${
                          isActive(item) ? 'w-full' : ''
                        }`}></span>
                      </>
                    );
                    return item.external ? (
                      <a
                        key={item.id}
                        href={item.path}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={linkClasses}
                        style={{ fontFamily: "Times New Roman, serif" }}
                      >
                        {scrolledLinkContent}
                      </a>
                    ) : (
                      <Link
                        key={item.id}
                        href={item.path}
                        className={linkClasses}
                        style={{ fontFamily: "Times New Roman, serif" }}
                      >
                        {scrolledLinkContent}
                      </Link>
                    );
                  })}
                </div>
              </nav>
            </div>
          </div>
          <div className="lg:hidden absolute top-1/2 left-4 -translate-y-1/2 z-[60]">
            <button
              ref={hamburgerRef}
              onClick={toggleMenu}
              className={`   ${
                isScrolled
                  ? 'text-black hover:bg-black/30 bg-black/20 border-black/50'
                  : 'text-black hover:bg-black/30 bg-black/15 border-black/40'
              }`}
              aria-label="Toggle menu"
            >
              <div className="w-6 h-6 relative">
                {menuOpen ? (
                  <X className="w-6 h-6 transition-transform duration-300 rotate-90" />
                ) : (
                  <Menu className="w-6 h-6 transition-transform duration-300 hover:scale-110" />
                )}
              </div>
            </button>
          </div>
        </div>
    </header>

    {menuOpen && (
      <div
        className="fixed inset-0 bg-black/90 backdrop-blur-xl z-[60] lg:hidden transition-opacity duration-300"
        onClick={closeMenu}
      />
    )}
    <nav
        ref={menuRef}
        className={`fixed top-0 left-0 h-full w-80 max-w-sm bg-white/95 backdrop-blur-2xl z-[70] transform transition-transform duration-500 ease-out lg:hidden border-r border-gray-300 ${
          menuOpen ? "translate-x-0" : "-translate-x-full"
        }`}
        style={{
          background: "linear-gradient(180deg, rgba(255,255,255,0.98) 0%, rgba(255,255,255,0.95) 100%)",
          boxShadow: "10px 0 50px rgba(0,0,0,0.2)"
        }}
      >
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1
                className="text-lg sm:text-lg font-light tracking-[0.25em] uppercase text-black whitespace-nowrap"
                style={{
                  fontFamily: 'Times New Roman, serif',
                  letterSpacing: '0.25em'
                }}
              >
                DIAMOND ATELIER
              </h1>
            </div>
            <button
              onClick={closeMenu}
              className="p-2 text-black hover:bg-black/10 rounded-lg transition-colors duration-200"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
        <div className="p-6 space-y-2">
          {navigationRoutes.map((item) => {
            const linkClasses = `block px-4 py-4 text-base font-light uppercase tracking-[0.15em] transition-all duration-300 rounded-lg ${
              isActive(item)
                ? "text-black bg-black/10 border border-black/20"
                : "text-black/80 hover:text-black hover:bg-black/5"
            }`;
            return item.external ? (
              <a
                key={item.id}
                href={item.path}
                target="_blank"
                rel="noopener noreferrer"
                onClick={closeMenu}
                className={linkClasses}
                style={{ fontFamily: "Times New Roman, serif" }}
              >
                {item.label}
              </a>
            ) : (
              <Link
                key={item.id}
                href={item.path}
                onClick={closeMenu}
                className={linkClasses}
                style={{ fontFamily: "Times New Roman, serif" }}
              >
                {item.label}
              </Link>
            );
          })}
        </div>
      </nav>
    </>
  );
};

export default Header;
