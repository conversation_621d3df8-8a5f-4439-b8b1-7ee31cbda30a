"use client";
import { useEffect, useRef, useState  } from "react";
import <PERSON><PERSON> from 'lottie-react';
import Image from "next/image";
import AnimatedHeading from "./AnimatedHeading";

const sections = [
  {
    id: "hero",
    title: "DIAMOND ATELIER",
    subtitle: "Crafting Excellence",
    description: "Discover the world's finest diamonds, meticulously crafted with precision and passion.",
    image: "/image/tache_diamond_rough_cut.png",
    bgColor: "bg-black"
  },
  {
    id: "quality",
    title: "Unmatched Quality",
    subtitle: "Every Diamond Tells a Story",
    description: "Our commitment to excellence ensures that every diamond meets the highest standards.",
    image: "/image/laser_phase_tache.png",
    bgColor: "bg-black"
  },
  {
    id: "craftsmanship",
    title: "Master Craftsmanship",
    subtitle: "Precision in Every Cut",
    description: "Skilled artisans combine traditional techniques with modern technology.",
    image: "/image/shaping_phase_tache-768x768.png",
    bgColor: "bg-black"
  },
  {
    id: "collection",
    title: "Exclusive Collection",
    subtitle: "Rare & Exceptional Diamonds",
    description: "Explore our curated selection of rare diamonds, each carefully selected.",
    image: "/image/download.png",
    bgColor: "bg-black"
  }
];

// Hook to get window dimensions
function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1920,
    height: typeof window !== 'undefined' ? window.innerHeight : 1080,
  });

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return windowSize;
}

export default function FullPageScroll() {
  const containerRef = useRef(null);
  const lottieRef = useRef(null);
  const [currentSection, setCurrentSection] = useState(0);
  const [imageSplitProgress, setImageSplitProgress] = useState({});
  const [isImageSplitting, setIsImageSplitting] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [showLottieAnimation, setShowLottieAnimation] = useState(false);
  const [currentAnimation, setCurrentAnimation] = useState(null);



  // Touch handling with refs
  const touchStartRef = useRef(null);
  const touchEndRef = useRef(null);

  // Load animation data
  useEffect(() => {
    const loadAnimations = async () => {
      try {
        const response1 = await fetch('/animations/tache_transition_pt1.json');
        const animation1 = await response1.json();
        // Store animation data for later use
        window.transitionPt1 = animation1;

        const response2 = await fetch('/animations/tache_transition_pt2.json');
        const animation2 = await response2.json();
        window.transitionPt2 = animation2;
      } catch (error) {
        console.log('Animation files not found, using CSS animations instead');
      }
    };

    loadAnimations();
  }, []);
  const windowSize = useWindowSize();

  const handleNavigation = useCallback((direction) => {
    if (isScrolling) return;

    setIsScrolling(true);
    

    if (direction === 'up' && currentSection > 0) {
      const targetSection = currentSection - 1;

      // Reset target section's image split progress
      setImageSplitProgress(prev => ({
        ...prev,
        [targetSection]: 0
      }));

      // Move to previous section
      setCurrentSection(targetSection);

      setTimeout(() => {
        setIsScrolling(false);
      }, 800);
    }
      else if (direction === 'down' && currentSection < sections.length - 1) {
        const currentProgress = imageSplitProgress[currentSection] || 0;
        
        // If image not split yet, split it first
        if (currentProgress === 0) {
          setIsImageSplitting(true);

          // Show Lottie animation
          if (window.transitionPt1) {
            setShowLottieAnimation(true);
            setCurrentAnimation(window.transitionPt1);
            if (lottieRef.current) {
              lottieRef.current.play();
            }
          }

          // Animate image split
          const splitDuration = 600;
          const startTime = Date.now();

          const animateSplit = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / splitDuration, 1);

            setImageSplitProgress(prev => ({
              ...prev,
              [currentSection]: progress
            }));

            if (progress < 1) {
              requestAnimationFrame(animateSplit);
            } else {
              setShowLottieAnimation(false);
              setIsImageSplitting(false);
              
              // Move to next section after split
              setTimeout(() => {
                setCurrentSection(prev => prev + 1);
                setIsScrolling(false);
              }, 200);
            }
          };

          requestAnimationFrame(animateSplit);
        } else {
          // Image already split, just move to next section
          setCurrentSection(prev => prev + 1);
          setTimeout(() => {
            setIsScrolling(false);
          }, 800);
        }
      } else {
        // At boundaries, just reset scrolling
        setIsScrolling(false);
      }
  }, [currentSection, isScrolling, imageSplitProgress]);

  useEffect(() => {
    let scrollTimeout;
    let splitTimeout;

    const handleWheel = (e) => {
      e.preventDefault();
      const direction = e.deltaY > 0 ? 'down' : 'up';
      handleNavigation(direction);
    };

    const handleKeyDown = (e) => {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        handleNavigation('down');
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        handleNavigation('up');
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false });
    }

    const handleTouchStart = (e) => {
      touchEndRef.current = null;
      touchStartRef.current = e.targetTouches[0].clientY;
    };

    const handleTouchMove = (e) => {
      touchEndRef.current = e.targetTouches[0].clientY;
    };

    const handleTouchEnd = () => {
      if (!touchStartRef.current || !touchEndRef.current) return;
      const distance = touchStartRef.current - touchEndRef.current;
      const isUpSwipe = distance > 50;
      const isDownSwipe = distance < -50;

      if (isUpSwipe) {
        handleNavigation('down');
      } else if (isDownSwipe) {
        handleNavigation('up');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    if (container) {
      container.addEventListener('touchstart', handleTouchStart);
      container.addEventListener('touchmove', handleTouchMove);
      container.addEventListener('touchend', handleTouchEnd);
    }

    return () => {
      if (container) {
        container.removeEventListener('wheel', handleWheel);
        container.removeEventListener('touchstart', handleTouchStart);
        container.removeEventListener('touchmove', handleTouchMove);
        container.removeEventListener('touchend', handleTouchEnd);
      }
      document.removeEventListener('keydown', handleKeyDown);
      clearTimeout(scrollTimeout);
      clearTimeout(splitTimeout);
    };
  }, [handleNavigation]);

  return (


    <div
      ref={containerRef}
      className="h-screen overflow-hidden relative z-10"
      style={{ height: '100vh' }}
    >
   
      {showLottieAnimation && currentAnimation && (
        <div className="fixed inset-0 z-50 flex items-center justify-center pointer-events-none">
          <Lottie
            ref={lottieRef}
            animationData={currentAnimation}
            loop={false}
            autoplay={true}
            style={{ width: '100%', height: '100%' }}
          />
        </div>
      )}
      
    
      <div className="fixed right-8 top-1/2 transform -translate-y-1/2 z-30 space-y-4 hidden">
        {sections.map((_, index) => (
          <div
            key={index}
            className={`w-3 h-3 rounded-full border-2 border-white transition-all duration-300 ${
              index === currentSection ? 'bg-white' : 'bg-transparent'
            }`}
          />
        ))}
      </div>
      
      {currentSection === 0 && (imageSplitProgress[0] || 0) === 0 && (
        <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-30 text-white text-center animate-bounce">
       
        </div>
      )}

      {sections.map((section, index) => (
        <div
          key={section.id}
          className={`absolute inset-0 transition-transform duration-1000 ease-in-out ${section.bgColor}`}
          style={{
            transform: `translateY(${(index - currentSection) * 100}%)`,
            zIndex: index === currentSection ? 10 : 1
          }}
        >
  
          {section.isFooter ? (
            <div className="absolute inset-0 flex flex-col items-center justify-center px-8 lg:px-16 relative overflow-hidden">
             
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute bottom-20 right-20 w-40 h-40 bg-gradient-to-r from-pink-400 to-blue-500 rounded-full blur-3xl animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full blur-2xl animate-pulse delay-500"></div>
              </div>

              <div className="max-w-6xl mx-auto text-center relative z-20 px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8 lg:gap-12 mb-6 sm:mb-8">
                </div>
              </div>
            </div>
          ) : (
            <>
              {/* Desktop Layout: Text Left, Image Right */}
              <div className="hidden lg:flex absolute inset-0 items-center justify-center px-8 xl:px-16">
                <div className="w-full max-w-7xl mx-auto flex items-center justify-between">
                  {/* Left Side - Text Content */}
                  <div className="w-1/2 pr-16 text-white">
                    <AnimatedHeading
                      key={`${section.id}-${currentSection}`}
                      text={section.title}
                      level="h1"
                      className="text-3xl xl:text-4xl 2xl:text-5xl mb-6 font-light leading-tight"
                      showUnderline={false}
                      animationDelay={0.2}
                      triggerOnMount={currentSection === index}
                    />
                    <p className="text-base xl:text-lg 2xl:text-xl leading-relaxed font-light text-gray-300">
                      {section.description}
                    </p>
                  </div>

                  {/* Right Side - Image with Split Animation */}
                  <div className="w-1/2 flex items-center justify-center">
                    <div className="relative w-80 h-80 xl:w-96 xl:h-96 2xl:w-[450px] 2xl:h-[450px]">
                      {/* Complete Image - Shows when current section and not splitting */}
                      <div
                        className="absolute w-full h-full rounded-full overflow-hidden transition-all duration-1000 ease-out z-10"
                        style={{
                          opacity: index === currentSection && (imageSplitProgress[index] || 0) === 0 ? 1 : 0,
                          transform: index === currentSection && (imageSplitProgress[index] || 0) === 0 ? 'translate(0, 0) scale(1)' : 'translate(0, 0) scale(0.8)',
                          transformOrigin: 'center center'
                        }}
                      >
                        <Image
                          src={section.image}
                          alt={section.title}
                          fill
                          className="object-cover"
                          priority={index === 0}
                        />
                      </div>

                      {/* Left Half - Goes to Bottom Left Corner */}
                      <div
                        className="absolute w-full h-full rounded-full overflow-hidden transition-all duration-1000 ease-out z-10"
                        style={{
                          clipPath: 'polygon(0 0, 50% 0, 50% 100%, 0 100%)',
                          opacity: (imageSplitProgress[index] || 0) > 0 ? 1 : 0,
                          transform: (() => {
                            const progress = imageSplitProgress[index] || 0;
                            if (progress > 0) {
                              return `translate(${-progress * (windowSize.width/2 + 100)}px, ${progress * (windowSize.height/2 + 100)}px) scale(${1 - progress * 0.4})`;
                            } else {
                              return 'translate(0, 0) scale(1)';
                            }
                          })(),
                          transformOrigin: 'center center'
                        }}
                      >
                        <Image
                          src={section.image}
                          alt={section.title}
                          fill
                          className="object-cover"
                          priority={index === 0}
                        />
                      </div>

                      {/* Right Half - Goes to Top Right Corner */}
                      <div
                        className="absolute w-full h-full rounded-full overflow-hidden transition-all duration-1000 ease-out z-10"
                        style={{
                          clipPath: 'polygon(50% 0, 100% 0, 100% 100%, 50% 100%)',
                          opacity: (imageSplitProgress[index] || 0) > 0 ? 1 : 0,
                          transform: (() => {
                            const progress = imageSplitProgress[index] || 0;
                            return progress > 0
                              ? `translate(${progress * (windowSize.width/2 + 100)}px, ${-progress * (windowSize.height/2 + 100)}px) scale(${1 - progress * 0.4})`
                              : 'translate(0, 0) scale(1)';
                          })(),
                          transformOrigin: 'center center'
                        }}
                      >
                        <Image
                          src={section.image}
                          alt={section.title}
                          fill
                          className="object-cover"
                          priority={index === 0}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Mobile Layout: Image Top, Text Bottom */}
              <div className="lg:hidden absolute inset-0 flex flex-col items-center justify-center px-8">
                {/* Top - Image with Split Animation */}
                <div className="flex items-center justify-center mb-8">
                  <div className="relative w-64 h-64 sm:w-80 sm:h-80">
                    {/* Complete Image - Shows when current section and not splitting */}
                    <div
                      className="absolute w-full h-full rounded-full overflow-hidden transition-all duration-1000 ease-out z-10"
                      style={{
                        opacity: index === currentSection && (imageSplitProgress[index] || 0) === 0 ? 1 : 0,
                        transform: index === currentSection && (imageSplitProgress[index] || 0) === 0 ? 'translate(0, 0) scale(1)' : 'translate(0, 0) scale(0.8)',
                        transformOrigin: 'center center'
                      }}
                    >
                      <Image
                        src={section.image}
                        alt={section.title}
                        fill
                        className="object-cover"
                        priority={index === 0}
                      />
                    </div>

                    {/* Left Half - Goes to Bottom Left Corner */}
                  
                    <div
                      className="absolute w-full h-full rounded-full overflow-hidden transition-all duration-1000 ease-out z-10"
                      style={{
                        clipPath: 'polygon(0 0, 50% 0, 50% 100%, 0 100%)',
                        opacity: (imageSplitProgress[index] || 0) > 0 ? 1 : 0,
                        transform: (() => {
                          const progress = imageSplitProgress[index] || 0;
                          if (progress > 0) {
                            return `translate(${-progress * (windowSize.width/2 + 100)}px, ${progress * (windowSize.height/2 + 100)}px) scale(${1 - progress * 0.4})`;
                          } else {
                            return 'translate(0, 0) scale(1)';
                          }
                        })(),
                        transformOrigin: 'center center'
                      }}
                    >
                      <Image
                        src={section.image}
                        alt={section.title}
                        fill
                        className="object-cover"
                        priority={index === 0}
                      />
                    </div>

                    <div
                      className="absolute w-full h-full rounded-full overflow-hidden transition-all duration-1000 ease-out z-10"
                      style={{
                        clipPath: 'polygon(50% 0, 100% 0, 100% 100%, 50% 100%)',
                        opacity: (imageSplitProgress[index] || 0) > 0 ? 1 : 0,
                        transform: (() => {
                          const progress = imageSplitProgress[index] || 0;
                          return progress > 0
                            ? `translate(${progress * (windowSize.width/2 + 100)}px, ${-progress * (windowSize.height/2 + 100)}px) scale(${1 - progress * 0.4})`
                            : 'translate(0, 0) scale(1)';
                        })(),
                        transformOrigin: 'center center'
                      }}
                    >
                      <Image
                        src={section.image}
                        alt={section.title}
                        fill
                        className="object-cover"
                        priority={index === 0}
                      />
                    </div>
                  </div>
                </div>

                {/* Bottom - Text Content */}
                <div className="text-center text-white max-w-4xl">
                  <AnimatedHeading
                    key={`${section.id}-${currentSection}`}
                    text={section.title}
                    level="h1"
                    className="text-2xl sm:text-3xl mb-4 sm:mb-6 font-light leading-tight"
                    showUnderline={false}
                    animationDelay={0.2}
                    triggerOnMount={currentSection === index}
                  />
                  <p className="text-sm sm:text-base leading-relaxed font-light text-gray-300">
                    {section.description}
                  </p>
                </div>
              </div>
            </>
          )}
        </div>
      ))}
    </div>
  );
}
