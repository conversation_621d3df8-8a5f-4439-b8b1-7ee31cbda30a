"use client";
import { useState,useEffect } from "react";
import Image from "next/image";
import AnimatedHeading from "./AnimatedHeading";
import { ChevronDown } from "lucide-react";
import Link from "next/link"
import { motion, useInView } from "framer-motion";

const sections = [
  {
    id: "hero",
    title: "Exquisite Lab Grown Diamonds",
    subtitle: "Every Diamond Tells a Story",
    description:
      "Ethically sourced, certified diamonds with unparalleled quality and brilliance.",
    image: "/image/tache_diamond_rough_cut.png",
    cta: "Discover Our Collection",
  },
  {
    id: "shapes",
    title: "Diamond Shapes",
    subtitle: "Classic & Contemporary",
    description:
      "Round, princess, emerald, oval and more. Find your perfect cut.",
    image: "/image/tache_lesotho_icon-768x752.jpg",
    cta: "View Shapes",
  },
  {
    id: "craftsmanship",
    title: "Master Craftsmanship",
    subtitle: "Precision in Every Cut",
    description:
      "Our skilled artisans combine traditional techniques with modern technology to create diamonds of exceptional beauty and brilliance.",
    image: "/image/shaping_phase_tache-768x768.png",
    cta: "View Process",
  },
  {
    id: "colors",
    title: "30+ Diamond Colors",
    subtitle: "Every Shade of Brilliance",
    description:
      "From classic white to rare fancy colors. Discover diamonds in over 30 stunning colors.",
    image:
      "/images/Colors/natural-color-dmds-color-diamonds-rough-diamonds-removebg-preview.png",
    cta: "View Colors",
  },
];

export default function SimpleHome() {
  const [activeSection, setActiveSection] = useState(0);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="bg-white min-h-screen pt-20">
      <div className="full-section relative w-full h-screen overflow-hidden">
        {/* Background Image */}
        <Image
          src="/image/Homepage_Banner_2025_Desktop_c0ddfa8194.png"
          alt="Diamond Atelier Homepage Banner"
          fill
          className="absolute inset-0 w-full h-full object-cover"
          priority
          quality={90}
        />

        {/* Sophisticated Overlay Gradients */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/60 pointer-events-none" />
        <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20 pointer-events-none" />

        {/* Animated Particles/Sparkles */}
        {isClient && (
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full opacity-60"
              initial={{
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                scale: 0,
              }}
              animate={{
                y: [null, -100],
                opacity: [0.6, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 3,
                ease: "easeOut",
              }}
            />
            ))}
          </div>
        )}

        {/* Main Content Container */}
        <div className="absolute inset-0 flex flex-col items-center justify-center z-20 px-6">
          {/* Logo Section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="text-center mb-8"
          >
            <div className="relative group">
              <h1
                className="text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-light leading-tight tracking-[0.2em] text-white/95 transition-all duration-700"
                style={{
                  fontFamily: "Times New Roman, serif",
                  // textShadow: '0 0 40px rgba(255,255,255,0.6), 0 0 80px rgba(255,255,255,0.3)',
                  letterSpacing: "0.2em",
                }}
              >
                DIAMONDS THAT DESERVE YOU
              </h1>

              {/* Premium Glow Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-3xl"></div>
            </div>
          </motion.div>

          {/* Elegant Tagline */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.5, delay: 0.5, ease: "easeOut" }}
            className="text-center max-w-4xl mx-auto mb-12"
          >
            <h1 className="text-lg sm:text-xl md:text-2xl lg:text-2xl xl:text-3xl font-light text-white/90 tracking-[0.3em] leading-relaxed">
              {/* <PermanentCounter
                targetNumber={10000}
                suffix="+ CERTIFIED STONES"
                duration={2000}
              /> */}{" "}
              10,000+ Certified Stones
            </h1>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-white/60 to-transparent mx-auto mt-6 mb-6"></div>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-white/70 font-light tracking-wide leading-relaxed">
              Where precision meets perfection in every facet
            </p>
          </motion.div>

          {/* Call to Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.5, delay: 1, ease: "easeOut" }}
            className="flex flex-col sm:flex-row gap-4 sm:gap-6"
          >
            <Link
              href="/shapes"
              className="group relative px-8 py-3 bg-transparent border border-white/30 text-white hover:bg-white hover:text-black transition-all duration-500 text-sm sm:text-base tracking-wider font-light overflow-hidden"
            >
              <span className="relative z-10">EXPLORE SHAPES</span>
              <div className="absolute inset-0 bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
            </Link>

            <Link
              href="https://inventory.diamondatelier.in/"
              className="group relative px-8 py-3 bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-500 text-sm sm:text-base tracking-wider font-light"
            >
              <span className="relative z-10">VIEW INVENTORY</span>
            </Link>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1.5, delay: 1.5 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 cursor-pointer"
          onClick={() => setCurrentIndex(1)}
        >
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="flex flex-col items-center text-white/60 hover:text-white transition-colors duration-300"
          >
            <span className="text-xs tracking-widest mb-2 font-light">
              SCROLL
            </span>
            <ChevronDown className="w-5 h-5" />
          </motion.div>
        </motion.div>
      </div>
      {/* Hero Section */}
      <section className="min-h-screen flex items-center justify-center px-4 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Desktop Layout */}
          <div className="hidden lg:flex items-center justify-between gap-16">
            {/* Left - Text Content */}
            <div className="flex-1 text-black">
              <div className="mb-4">
                <span className="text-gray-600 text-sm uppercase tracking-[0.2em] font-light">
                  {sections[0].subtitle}
                </span>
              </div>
              <AnimatedHeading
                text={sections[0].title}
                level="h1"
                className="text-5xl xl:text-6xl 2xl:text-7xl mb-8 font-light leading-tight"
                showUnderline={false}
                animationDelay={0.2}
                triggerOnMount={true}
              />
              <p className="text-xl xl:text-2xl leading-relaxed font-light text-gray-700 mb-12 max-w-2xl">
                {sections[0].description}
              </p>
              <button className="bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300">
                {sections[0].cta}
              </button>
            </div>

            {/* Right - Image */}
            <div className="flex-1 flex justify-center">
              <div className="relative w-96 h-96 xl:w-[500px] xl:h-[500px] 2xl:w-[600px] 2xl:h-[600px]">
                <Image
                  src={sections[0].image}
                  alt={sections[0].title}
                  fill
                  className="object-cover rounded-full"
                  priority
                />
              </div>
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="lg:hidden text-center text-black">
            <div className="mb-6">
              <span className="text-gray-600 text-sm uppercase tracking-[0.2em] font-light">
                {sections[0].subtitle}
              </span>
            </div>
            <AnimatedHeading
              text={sections[0].title}
              level="h1"
              className="text-4xl sm:text-5xl mb-8 font-light leading-tight"
              showUnderline={false}
              animationDelay={0.2}
              triggerOnMount={true}
            />
            <div className="mb-8 flex justify-center">
              <div className="relative w-80 h-80 sm:w-96 sm:h-96">
                <Image
                  src={sections[0].image}
                  alt={sections[0].title}
                  fill
                  className="object-cover rounded-full"
                  priority
                />
              </div>
            </div>
            <p className="text-lg leading-relaxed font-light text-gray-700 mb-8 max-w-2xl mx-auto">
              {sections[0].description}
            </p>
            <button className="bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300">
              {sections[0].cta}
            </button>
          </div>
        </div>
      </section>

      {/* Other Sections */}
      {sections.slice(1).map((section, index) => (
        <section
          key={section.id}
          className="min-h-screen flex items-center justify-center px-4 lg:px-8 border-t border-gray-200"
        >
          <div className="max-w-7xl mx-auto">
            {/* Desktop Layout - Alternating */}
            <div
              className={`hidden lg:flex items-center justify-between gap-16 ${
                index % 2 === 0 ? "flex-row-reverse" : ""
              }`}
            >
              {/* Text Content */}
              <div className="flex-1 text-black">
                <div className="mb-4">
                  <span className="text-gray-600 text-sm uppercase tracking-[0.2em] font-light">
                    {section.subtitle}
                  </span>
                </div>
                <h2 className="text-4xl xl:text-5xl mb-6 font-light leading-tight">
                  {section.title}
                </h2>
                <p className="text-lg xl:text-xl leading-relaxed font-light text-gray-700 mb-8 max-w-2xl">
                  {section.description}
                </p>
                <button className="border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300">
                  {section.cta}
                </button>
              </div>

              {/* Image */}
              <div className="flex-1 flex justify-center">
                <div className="relative w-80 h-80 xl:w-96 xl:h-96">
                  <Image
                    src={section.image}
                    alt={section.title}
                    fill
                    className="object-cover rounded-full"
                  />
                </div>
              </div>
            </div>

            {/* Mobile Layout */}
            <div className="lg:hidden text-center text-black">
              <div className="mb-4">
                <span className="text-gray-600 text-sm uppercase tracking-[0.2em] font-light">
                  {section.subtitle}
                </span>
              </div>
              <h2 className="text-3xl sm:text-4xl mb-6 font-light leading-tight">
                {section.title}
              </h2>
              <div className="mb-6 flex justify-center">
                <div className="relative w-72 h-72 sm:w-80 sm:h-80">
                  <Image
                    src={section.image}
                    alt={section.title}
                    fill
                    className="object-cover rounded-full"
                  />
                </div>
              </div>
              <p className="text-base leading-relaxed font-light text-gray-700 mb-6 max-w-2xl mx-auto">
                {section.description}
              </p>
              <button className="border border-black text-black px-8 py-3 text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300">
                {section.cta}
              </button>
            </div>
          </div>
        </section>
      ))}

<section className="relative w-full h-96 overflow-hidden">
        <div
          className="absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: "url('/image/Expert.jpg')"
          }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/40 to-transparent"></div>
        <div className="relative z-10 h-full flex items-center">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
            <div className="max-w-2xl">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-black mb-6 drop-shadow-lg">
                Expert Eyes on Every Stone
              </h2>
              <p className="text-lg text-black mb-8 leading-relaxed drop-shadow-md">
                Our in-house gemologists personally inspect and verify every lab-grown diamond. You&apos;ll receive a detailed report covering brilliance, cut, and quality — far beyond what a certificate alone can show.
              </p>
            </div>
          </div>
        </div>
      </section>
      {/* Navigation Dots */}
      <div className="fixed right-8 top-1/2 transform -translate-y-1/2 z-40 hidden lg:block">
        <div className="flex flex-col space-y-4">
          {sections.map((_, index) => (
            <button
              key={index}
              onClick={() => setActiveSection(index)}
              className={`w-3 h-3 rounded-full border-2 transition-all duration-300 ${
                activeSection === index
                  ? "bg-black border-black"
                  : "bg-transparent border-gray-400 hover:border-black"
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
