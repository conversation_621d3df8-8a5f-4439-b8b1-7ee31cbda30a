"use client";

import React, { useState, useEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { Menu, X, Search, User, ShoppingBag } from "lucide-react";

const LuxuryHeader = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const menuRef = useRef(null);
  const hamburgerRef = useRef(null);
  const pathname = usePathname();

  // Navigation routes - Luxury brand style
  const navigationRoutes = [
    { id: 1, label: "INVENTORY", path: "https://inventory.diamondatelier.in/", external: true },
    { id: 2, label: "SHAPES", path: "/shapes" },
    // { id: 3, label: "BLOGS", path: "/blogs" },
    { id: 4, label: "EDUCATION", path: "#" },
    { id: 5, label: "ABOUT", path: "#" },
    { id: 6, label: "CONTACT", path: "#" }
  ];

  // Optimized scroll detection
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const scrollY = window.scrollY;
          setIsScrolled(scrollY > 20);
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        menuOpen &&
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        hamburgerRef.current &&
        !hamburgerRef.current.contains(event.target)
      ) {
        setMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [menuOpen]);

  const toggleMenu = () => setMenuOpen(!menuOpen);
  const closeMenu = () => setMenuOpen(false);
  const isActive = (item) => pathname === item.path;

  return (
    <header className={`fixed top-0 w-full z-[100] transition-all duration-500 ${
      isScrolled
        ? 'bg-white/98 backdrop-blur-xl shadow-2xl border-b border-gray-200'
        : 'bg-white/95 backdrop-blur-sm'
    }`}>

        {/* Elegant Top Border */}
        <div className="w-full h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>

        <div className="max-w-6xl mx-auto px-6 sm:px-8 lg:px-16">

          {/* Top Row - Logo Only (Desktop) / Logo + Mobile Menu (Mobile) */}
          <div className="flex items-center justify-between lg:justify-center py-2">
            {/* Mobile Menu Button - Left Side (Mobile Only) */}
            <button
              ref={hamburgerRef}
              onClick={toggleMenu}
              className="lg:hidden p-2 text-gray-700 hover:text-black transition-colors duration-300 hover:bg-gray-100 rounded-md z-10"
              aria-label="Toggle menu"
            >
              {menuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>

            {/* Logo - Always Centered */}
            <Link href="/" className="group relative flex-1 lg:flex-none">
              <div className="text-center">
                <h1
                  className={`font-light text-black tracking-[0.25em] transition-all duration-500 group-hover:tracking-[0.3em] ${
                    isScrolled ? 'text-xl lg:text-xl' : 'text-xl lg:text-2xl'
                  }`}
                  style={{
                    fontFamily: 'Playfair Display, Times New Roman, serif',
                    textShadow: '0 1px 2px rgba(0,0,0,0.1)'
                  }}
                >
                  DIAMOND ATELIER
                </h1>
                <div className="w-full h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-black/3 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-2xl"></div>
            </Link>

            {/* Spacer for mobile balance */}
            <div className="lg:hidden w-10"></div>
          </div>

          {/* Separator Line */}
          <div className="w-full h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>

          {/* Bottom Row - All Navigation in One Line */}
          <div className="flex items-center justify-center py-4">
            <nav className="hidden lg:flex items-center space-x-12">
              {navigationRoutes.map((item) => {
                const linkClasses = `text-sm font-light text-gray-600 hover:text-black transition-all duration-400 tracking-[0.1em] relative group uppercase ${
                  isActive(item) ? "text-black" : ""
                }`;

                const linkContent = (
                  <>
                    {item.label}
                    <span className={`absolute -bottom-3 left-0 w-0 h-0.5 bg-black transition-all duration-500 group-hover:w-full ${
                      isActive(item) ? 'w-full' : ''
                    }`}></span>
                  </>
                );

                return item.external ? (
                  <a
                    key={item.id}
                    href={item.path}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={linkClasses}
                  >
                    {linkContent}
                  </a>
                ) : (
                  <Link key={item.id} href={item.path} className={linkClasses}>
                    {linkContent}
                  </Link>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Mobile Menu Background Overlay */}
        {menuOpen && (
          <div
            className="lg:hidden fixed inset-0 bg-black/50 z-[98]"
            onClick={closeMenu}
          />
        )}

        {/* Mobile Menu */}
        {menuOpen && (
          <div className="lg:hidden fixed top-[120px] left-0 right-0 bg-white z-[99] border-t border-gray-200 shadow-xl">
            <nav ref={menuRef} className="flex flex-col py-4 px-6">
              {navigationRoutes.map((item) => {
                const linkClasses = `py-4 text-lg font-medium text-gray-800 hover:text-black transition-all duration-300 tracking-[0.1em] uppercase border-b border-gray-100 last:border-b-0 ${
                  isActive(item) ? "text-black font-semibold bg-gray-50" : ""
                }`;

                return item.external ? (
                  <a
                    key={item.id}
                    href={item.path}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={linkClasses}
                    onClick={closeMenu}
                  >
                    {item.label}
                  </a>
                ) : (
                  <Link
                    key={item.id}
                    href={item.path}
                    className={linkClasses}
                    onClick={closeMenu}
                  >
                    {item.label}
                  </Link>
                );
              })}
            </nav>
          </div>
        )}
      </header>
  );
};

export default LuxuryHeader;
